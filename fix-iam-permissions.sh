#!/bin/bash

# Fix IAM permissions for Firebase Cloud Functions to create custom tokens
# This script grants the necessary permissions to the Cloud Functions service account
# Specifically addresses 'iam.serviceAccounts.signBlob' permission for Firebase Auth

set -e  # Exit on any error

echo "🔧 Firebase Functions IAM Permissions Fix for Dev Project"
echo "========================================================="

# Get project ID from .firebaserc or use default dev project
if [ -f ".firebaserc" ]; then
    PROJECT_ID=$(cat .firebaserc | grep '"dev"' | sed 's/.*"dev": *"\([^"]*\)".*/\1/')
    echo "📋 Found dev project ID in .firebaserc: $PROJECT_ID"
else
    PROJECT_ID="marketplace-dev-76a4a"
    echo "📋 Using default dev project ID: $PROJECT_ID"
fi

if [ -z "$PROJECT_ID" ]; then
    echo "❌ Error: Project ID is required"
    exit 1
fi

echo ""
echo "🚀 Configuring IAM permissions for project: $PROJECT_ID"

# Define the Cloud Functions service account
FUNCTIONS_SA="serviceAccount:${PROJECT_ID}@appspot.gserviceaccount.com"
echo "Service Account: $FUNCTIONS_SA"
echo ""

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Error: gcloud CLI is not installed"
    echo "Please install it from: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Check if user is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
    echo "❌ Error: Not authenticated with gcloud"
    echo "Please run: gcloud auth login"
    exit 1
fi

echo "✅ gcloud CLI is ready"

# Check existing roles for the service account
echo "🔍 Checking existing IAM roles for $FUNCTIONS_SA..."
CURRENT_ROLES=$(gcloud projects get-iam-policy $PROJECT_ID \
    --flatten="bindings[].members" \
    --format="value(bindings.role)" \
    --filter="bindings.members:$FUNCTIONS_SA")
echo "Current roles: $CURRENT_ROLES"

# Function to grant a role if not already assigned
grant_role() {
    local role=$1
    local role_name=$(basename $role)
    if echo "$CURRENT_ROLES" | grep -q "$role"; then
        echo "✅ Role $role_name already granted"
    else
        echo "🔑 Granting $role_name role..."
        if gcloud projects add-iam-policy-binding $PROJECT_ID \
            --member="$FUNCTIONS_SA" \
            --role="$role" \
            --quiet; then
            echo "✅ $role_name role granted"
        else
            echo "❌ Failed to grant $role_name role"
            exit 1
        fi
    fi
}

# Grant Service Account Token Creator role (includes iam.serviceAccounts.signBlob)
grant_role "roles/iam.serviceAccountTokenCreator"

# Grant Firebase Admin SDK Admin Service Agent role (for broader Firebase Admin SDK permissions)
grant_role "roles/firebase.sdkAdminServiceAgent"

# Optionally grant Service Account User role (if needed for other operations)
grant_role "roles/iam.serviceAccountUser"

# Note: roles/iam.serviceAccountKeyAdmin is not needed for signBlob
# Removed to minimize permissions unless explicitly required
# If needed, uncomment the following line:
# grant_role "roles/iam.serviceAccountKeyAdmin"

echo ""
echo "🎉 All required IAM permissions have been granted successfully!"
echo ""
echo "📝 Next steps:"
echo "1. Build your functions: cd functions && npm run build"
echo "2. Deploy your functions: firebase deploy --only functions"
echo "3. Test the Telegram authentication function (authenticateWithTelegram)"
echo ""
echo "🔍 To verify permissions, run:"
echo "gcloud projects get-iam-policy $PROJECT_ID --flatten=\"bindings[].members\" --format=\"table(bindings.role)\" --filter=\"bindings.members:$FUNCTIONS_SA\""
echo ""
echo "⚠️ If the error persists, ensure the Firebase Admin SDK is initialized correctly in your function code."
echo "Refer to: https://firebase.google.com/docs/auth/admin/create-custom-tokens"