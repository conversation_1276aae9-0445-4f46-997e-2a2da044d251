import { mnemonicToPrivate<PERSON><PERSON> } from "@ton/crypto";
import { SendMode, TonClient, WalletContractV5R1, internal } from "@ton/ton";
import { getMarketplaceWalletMnemonic, isDevelopment } from "../config";
import { getHttpEndpoint } from "@orbs-network/ton-access";

export class TonWalletService {
  private client: TonClient | null = null;
  private marketplaceWallet: WalletContractV5R1 | null = null;
  private keyPair: { publicKey: Buffer; secretKey: Buffer } | null = null;

  async initialize(): Promise<void> {
    if (this.client && this.marketplaceWallet && this.keyPair) {
      return; // Already initialized
    }

    const network = isDevelopment() ? "testnet" : "mainnet";
    const endpoint = await getHttpEndpoint({ network });

    this.client = new TonClient({ endpoint });

    const marketplaceWalletMnemonic = getMarketplaceWalletMnemonic();
    this.keyPair = await mnemonicToPrivateKey(
      marketplaceWalletMnemonic.split(" ")
    );

    const workchain = 0;
    this.marketplaceWallet = WalletContractV5R1.create({
      workchain,
      publicKey: this.keyPair.publicKey,
    });
  }

  async sendTransfer(params: {
    amount: number;
    toAddress: string;
    body: string;
  }) {
    await this.initialize();

    if (!this.client || !this.marketplaceWallet || !this.keyPair) {
      throw new Error("TON wallet service not properly initialized");
    }

    const marketplaceContract = this.client.open(this.marketplaceWallet);

    // Get sequence number for the transaction
    const seqno = await this.marketplaceWallet.getSeqno(
      this.client.provider(this.marketplaceWallet.address)
    );

    // Create transfer transaction
    const transfer = marketplaceContract.createTransfer({
      seqno,
      sendMode: SendMode.PAY_GAS_SEPARATELY,
      secretKey: this.keyPair.secretKey,
      messages: [
        internal({
          value: params.amount.toString(),
          to: params.toAddress,
          body: params.body,
        }),
      ],
    });

    // Send the transaction
    await marketplaceContract.send(transfer);

    const transactionHash = transfer.hash().toString("hex");

    console.log(
      `TON transfer completed: ${params.amount} TON sent to ${params.toAddress} (${params.body})`
    );

    return {
      success: true,
      transactionHash,
      message: `Successfully transferred ${params.amount} TON`,
    };
  }

  async sendWithdrawal(amount: number, userWalletAddress: string) {
    return this.sendTransfer({
      amount,
      toAddress: userWalletAddress,
      body: "Withdrawal from marketplace",
    });
  }

  async sendRevenueTransfer(amount: number, johnDowWallet: string) {
    return this.sendTransfer({
      amount,
      toAddress: johnDowWallet,
      body: "Revenue transfer - John Dow",
    });
  }
}

let tonWalletServiceInstance: TonWalletService | null = null;

export function getTonWalletService() {
  tonWalletServiceInstance ??= new TonWalletService();
  return tonWalletServiceInstance;
}
