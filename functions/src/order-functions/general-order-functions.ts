import * as admin from "firebase-admin";
import { on<PERSON>all, HttpsError } from "firebase-functions/v2/https";
import { OrderEntity, OrderStatus } from "../types";
import {
  processOrderCancellation,
  validateCancellationPermission,
} from "../services/order-cancellation-service";
import { requireAuthentication } from "../services/auth-middleware";
import { CORS_CONFIG } from "../config";

export const cancelUserOrder = onCall<{
  orderId: string;
  userId: string;
}>({ cors: CORS_CONFIG }, async (request) => {
  console.log("process.env", JSON.stringify(process.env));

  const authRequest = requireAuthentication(request);
  const { orderId, userId } = request.data;

  if (!orderId || !userId) {
    throw new HttpsError(
      "invalid-argument",
      "orderId and userId are required."
    );
  }

  if (authRequest?.auth?.uid !== userId) {
    throw new HttpsError(
      "permission-denied",
      "You can only cancel your own orders."
    );
  }

  try {
    const db = admin.firestore();

    const orderDoc = await db.collection("orders").doc(orderId).get();
    if (!orderDoc.exists) {
      throw new HttpsError("not-found", "Order not found.");
    }

    const order = { id: orderDoc.id, ...orderDoc.data() } as OrderEntity;

    // Validate cancellation permission using the new service
    validateCancellationPermission(order, userId);

    // Process the cancellation using unified logic
    const result = await processOrderCancellation(order, userId);

    return {
      success: result.success,
      message: result.message,
      order: {
        id: order.id,
        number: order.number,
        status: OrderStatus.CANCELLED,
      },
      feeApplied: result.feeApplied,
      feeType: result.feeType,
    };
  } catch (error) {
    console.error("Error cancelling order:", error);

    if (error instanceof Error) {
      throw new HttpsError("failed-precondition", error.message);
    }

    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while cancelling order."
    );
  }
});
