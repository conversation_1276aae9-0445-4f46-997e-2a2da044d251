'use client';

import { Caption } from '@telegram-apps/telegram-ui';
import { AlertTriangle, ArrowUp } from 'lucide-react';
import { Drawer } from 'vaul';

import { CountdownPopup } from '@/components/CountdownPopup';
import { TonLogo } from '@/components/TonLogo';

import { DepositDrawerActions } from './deposit-drawer-actions';
import { DepositDrawerAmountInput } from './deposit-drawer-amount-input';
import {
  useDepositState,
  useDepositTransaction,
  useDepositValidation,
} from './deposit-drawer-hooks';
import { DepositDrawerSummary } from './deposit-drawer-summary';

interface DepositDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function DepositDrawer({ open, onOpenChange }: DepositDrawerProps) {
  const {
    depositAmount,
    setDepositAmount,
    showCountdownPopup,
    resetForm,
    openCountdownPopup,
    closeCountdownPopup,
  } = useDepositState();

  const { validateAmount, appConfig } = useDepositValidation();
  const { executeDeposit, refetchUserData, loading, isWalletConnected } =
    useDepositTransaction();

  const isValidAmount = validateAmount(depositAmount);
  const hasDepositAmount = !!depositAmount;
  const totalAmount =
    hasDepositAmount && isValidAmount && appConfig
      ? parseFloat(depositAmount) + appConfig.deposit_fee
      : undefined;

  const handleDeposit = async () => {
    if (!isValidAmount) return;

    const success = await executeDeposit(depositAmount);
    if (success) {
      resetForm();
      onOpenChange(false);
      openCountdownPopup();
    }
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  const handleCountdownComplete = async () => {
    await refetchUserData();
    closeCountdownPopup();
  };

  return (
    <Drawer.Root
      open={open}
      onOpenChange={onOpenChange}
      shouldScaleBackground
      modal
      dismissible
    >
      <Drawer.Portal>
        <Drawer.Title />
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[100]" />
        <Drawer.Content className="bg-[#17212b] flex flex-col rounded-t-[20px] mt-16 fixed bottom-0 left-0 right-0 z-[101] outline-none focus:outline-none">
          <div className="p-6 bg-[#17212b] rounded-t-[20px] flex-1 max-h-[85vh] overflow-y-auto">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#708499] mb-6 cursor-grab active:cursor-grabbing touch-manipulation" />

            <div className="max-w-md mx-auto space-y-6">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-10 h-10 bg-[#6ab2f2] rounded-full flex items-center justify-center">
                  <ArrowUp className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-semibold text-[#f5f5f5]">
                    Deposit Funds
                  </h2>
                  <Caption level="2" weight="3" className="text-[#708499]">
                    Add TON to your marketplace balance
                  </Caption>
                </div>
              </div>

              {!appConfig ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#6ab2f2] mx-auto"></div>
                  <Caption level="2" weight="3" className="text-[#708499] mt-2">
                    Loading configuration...
                  </Caption>
                </div>
              ) : (
                <>
                  <div className="bg-[#232e3c]/50 rounded-2xl p-4 border border-[#3a4a5c]/30">
                    <div className="w-full flex items-start gap-3">
                      <AlertTriangle className="w-5 h-5 text-[#6ab2f2] flex-shrink-0 mt-0.5" />
                      <div className="w-full text-sm">
                        <p className="w-full font-medium text-[#f5f5f5] mb-2">
                          Deposit Information
                        </p>
                        <div className="space-y-2">
                          <div className="w-f flex justify-between items-center py-1">
                            <span className="text-[#708499]">
                              Minimum deposit:
                            </span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {appConfig.min_deposit_amount}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                          <div className="flex justify-between items-center py-1">
                            <span className="text-[#708499]">Deposit fee:</span>
                            <div className="flex items-center gap-1">
                              <span className="text-[#6ab2f2] font-semibold">
                                {appConfig.deposit_fee}
                              </span>
                              <TonLogo size={14} />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <DepositDrawerAmountInput
                      value={depositAmount}
                      onChange={setDepositAmount}
                      minAmount={appConfig.min_deposit_amount}
                      isValid={isValidAmount}
                      hasValue={hasDepositAmount}
                    />

                    {hasDepositAmount && isValidAmount && (
                      <DepositDrawerSummary
                        depositAmount={depositAmount}
                        depositFee={appConfig.deposit_fee}
                      />
                    )}

                    <DepositDrawerActions
                      onDeposit={handleDeposit}
                      onCancel={handleClose}
                      {...{
                        isValidAmount,
                        loading,
                        isWalletConnected,
                        depositAmount,
                        totalAmount,
                      }}
                    />
                  </div>
                </>
              )}
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>

      <CountdownPopup
        show={showCountdownPopup}
        onClose={closeCountdownPopup}
        onComplete={handleCountdownComplete}
        initialSeconds={60}
        title="Deposit Processing"
        message="You will receive your funds within"
      />
    </Drawer.Root>
  );
}
