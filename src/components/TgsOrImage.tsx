'use client';

import Image from 'next/image';

import { useGiftImagePath } from '@/hooks/use-gift-image-path';

import { TgsViewer } from './TgsViewer';

interface TgsOrImageProps {
  isImage: boolean;
  collectionId: string;
  imageProps?: {
    alt: string;
    fill?: boolean;
    className?: string;
    loading?: 'lazy' | 'eager';
    sizes?: string;
    onError?: (e: React.SyntheticEvent<HTMLImageElement, Event>) => void;
  };
  tgsProps?: {
    style?: React.CSSProperties;
    className?: string;
  };
}

export function TgsOrImage({
  isImage,
  collectionId,
  imageProps,
  tgsProps,
}: TgsOrImageProps) {
  const imagePath = useGiftImagePath({ collectionId, format: 'png' });

  const tgsPath = useGiftImagePath({ collectionId, format: 'tgs' });

  if (!collectionId) {
    return null;
  }

  if (isImage && imageProps) {
    if (!imagePath.src) {
      return null;
    }

    return (
      <Image
        src={imagePath.src}
        alt={imageProps.alt}
        fill={imageProps.fill}
        className={imageProps.className}
        loading={imageProps.loading}
        sizes={imageProps.sizes}
        onError={(e) => {
          imagePath.onError();
          imageProps.onError?.(e);
        }}
      />
    );
  }

  if (!isImage && tgsProps) {
    return (
      <TgsViewer
        tgsUrl={tgsPath.src}
        style={tgsProps.style}
        onError={tgsPath.onError}
        className={tgsProps.className}
      />
    );
  }

  return null;
}
