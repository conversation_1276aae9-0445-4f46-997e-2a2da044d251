import type { DocumentSnapshot } from 'firebase/firestore';
import { useCallback, useRef, useState } from 'react';

import { getOrdersForBuyers, getOrdersForSellers } from '@/api/orders-api';
import type { OrderEntity } from '@/constants/core.constants';

export type TabType = 'sellers' | 'buyers';
export type SortType = 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';

interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy: SortType;
  currentUserId?: string;
}

interface OrderState {
  orders: OrderEntity[];
  loading: boolean;
  loadingMore: boolean;
  hasMore: boolean;
  lastDoc: DocumentSnapshot | null;
}

interface UseMarketplaceOrdersProps {
  activeTab: TabType;
  filters: OrderFilters;
}

interface UseMarketplaceOrdersReturn {
  sellersState: OrderState;
  buyersState: OrderState;
  loadOrders: (reset?: boolean) => Promise<void>;
  loadMoreOrders: () => void;
  resetOrders: () => void;
}

const createInitialState = (): OrderState => ({
  orders: [],
  loading: false,
  loadingMore: false,
  hasMore: true,
  lastDoc: null,
});

const addOrdersWithoutDuplicates = (
  existingOrders: OrderEntity[],
  newOrders: OrderEntity[],
  tabType: TabType,
): OrderEntity[] => {
  const existingIds = new Set(existingOrders.map((order) => order.id));
  const filteredNewOrders = newOrders.filter(
    (order) => !existingIds.has(order.id),
  );
  const duplicates = newOrders.filter((order) => existingIds.has(order.id));

  if (duplicates.length > 0) {
    console.warn(
      `🚨 Found duplicate orders for ${tabType}:`,
      duplicates.map((o) => o.id),
    );
  }

  return [...existingOrders, ...filteredNewOrders];
};

export const useMarketplaceOrders = ({
  activeTab,
  filters,
}: UseMarketplaceOrdersProps): UseMarketplaceOrdersReturn => {
  const [sellersState, setSellersState] =
    useState<OrderState>(createInitialState);
  const [buyersState, setBuyersState] =
    useState<OrderState>(createInitialState);

  const sellersLastDocRef = useRef<DocumentSnapshot | null>(null);
  const buyersLastDocRef = useRef<DocumentSnapshot | null>(null);

  const loadOrders = useCallback(
    async (reset = true) => {
      const isSellersTab = activeTab === 'sellers';
      const setState = isSellersTab ? setSellersState : setBuyersState;
      const lastDocRef = isSellersTab ? sellersLastDocRef : buyersLastDocRef;

      if (reset) {
        setState((prev) => ({
          ...prev,
          loading: true,
          orders: [],
          hasMore: true,
        }));
        lastDocRef.current = null;
      } else {
        setState((prev) => ({ ...prev, loadingMore: true }));
      }

      try {
        const validSortBy =
          typeof filters.sortBy === 'string' &&
          ['price_asc', 'price_desc', 'date_asc', 'date_desc'].includes(
            filters.sortBy,
          )
            ? filters.sortBy
            : 'date_desc';

        const requestFilters = {
          ...filters,
          sortBy: validSortBy,
          limit: 3,
          lastDoc: reset ? null : lastDocRef.current,
        };

        const result = isSellersTab
          ? await getOrdersForSellers(requestFilters)
          : await getOrdersForBuyers(requestFilters);

        setState((prev) => {
          if (reset) {
            return {
              ...prev,
              orders: result.orders,
              hasMore: result.hasMore,
            };
          } else {
            return {
              ...prev,
              orders: addOrdersWithoutDuplicates(
                prev.orders,
                result.orders,
                activeTab,
              ),
              hasMore: result.hasMore,
            };
          }
        });

        lastDocRef.current = result.lastDoc;
      } catch (error) {
        console.error('Error loading orders:', error);
      } finally {
        setState((prev) => ({
          ...prev,
          loading: false,
          loadingMore: false,
        }));
      }
    },
    [activeTab, filters],
  );

  const loadMoreOrders = useCallback(() => {
    const currentState = activeTab === 'sellers' ? sellersState : buyersState;
    const isLoading = currentState.loading || currentState.loadingMore;

    if (currentState.hasMore && !isLoading) {
      loadOrders(false);
    }
  }, [activeTab, sellersState, buyersState, loadOrders]);

  const resetOrders = useCallback(() => {
    setSellersState(createInitialState());
    setBuyersState(createInitialState());
    sellersLastDocRef.current = null;
    buyersLastDocRef.current = null;
  }, []);

  return {
    sellersState,
    buyersState,
    loadOrders,
    loadMoreOrders,
    resetOrders,
  };
};
