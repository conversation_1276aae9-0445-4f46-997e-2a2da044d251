'use client';

import { TonCon<PERSON>UIProvider } from '@tonconnect/ui-react';

import { RootLayoutHeader } from '@/app/(app)/root-layout-header';
import { TelegramProvider } from '@/components/TelegramProvider';
import { WALLET_MANIFEST_URL } from '@/utils/ton-constants';

import RootLayoutFooter from './root-layout-footer';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <TelegramProvider>
      <TonConnectUIProvider manifestUrl={WALLET_MANIFEST_URL}>
        <RootLayoutHeader />
        <div className="min-h-screen flex flex-col pt-[59px] pb-16">
          <main className="flex-1 p-2">
            <div className="max-w-6xl mx-auto">{children}</div>
          </main>
        </div>

        <RootLayoutFooter />
      </TonConnectUIProvider>
    </TelegramProvider>
  );
}
