import './globals.css';

import type { Metadata } from 'next';
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import { Toaster } from 'sonner';

import { RootProvider } from '@/root-context';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Marketplace',
  description: 'Telegram Gifts Marketplace',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-[#17212b] text-[#f5f5f5]`}
        suppressHydrationWarning
      >
        <RootProvider>{children}</RootProvider>

        <Toaster
          theme="dark"
          toastOptions={{
            style: {
              background: '#232e3c',
              border: '1px solid #3a4a5c',
              color: '#f5f5f5',
            },
          }}
        />
      </body>
    </html>
  );
}
