import axios from "axios";
import { loadEnvironment } from "./config/env-loader";

loadEnvironment();

const FIREBASE_PROJECT_ID = process.env.FIREBASE_PROJECT_ID;
const FIREBASE_REGION = process.env.FIREBASE_REGION ?? "us-central1";
const BOT_TOKEN = process.env.BOT_TOKEN;

if (!FIREBASE_PROJECT_ID) {
  throw new Error("FIREBASE_PROJECT_ID is required in environment variables");
}

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

// Cloud Run URLs for migrated functions
const CLOUD_RUN_BASE_URL = process.env.CLOUD_RUN_BASE_URL as string;

// Legacy Cloud Functions URL (for functions not yet migrated)
const FIREBASE_FUNCTIONS_BASE_URL = `https://${FIREBASE_REGION}-${FIREBASE_PROJECT_ID}.cloudfunctions.net`;

// Helper function to get the correct URL for each function
const getFunctionUrl = (functionName: string): string => {
  // List of functions that have been migrated to Cloud Run
  const migratedFunctions = [
    "sendGiftToRelayerByBot",
    "completePurchaseByBot",
    "getOrderByIdByBot",
    "getUserOrdersByBot",
  ];

  if (migratedFunctions.includes(functionName)) {
    // Convert camelCase to lowercase with hyphens for Cloud Run URLs
    const cloudRunName = functionName
      .toLowerCase()
      .replace(/([A-Z])/g, "-$1")
      .replace(/^-/, "");
    return `https://${cloudRunName}-${CLOUD_RUN_BASE_URL.split("//")[1]}`;
  }

  // Use legacy Cloud Functions URL for non-migrated functions
  return `${FIREBASE_FUNCTIONS_BASE_URL}/${functionName}`;
};

export enum OrderStatus {
  ACTIVE = "active",
  PAID = "paid",
  GIFT_SENT_TO_RELAYER = "gift_sent_to_relayer",
  FULFILLED = "fulfilled",
  CANCELLED = "cancelled",
}

export interface OrderEntity {
  id: string;
  number: number;
  buyerId?: string;
  sellerId?: string;
  price: number;
  status: OrderStatus;
  deadline?: any; // Deadline for seller to fulfill the order
  owned_gift_id?: string; // ID of the gift owned by the user creating the order
  secondaryMarketPrice?: number | null; // Price set by buyer for reselling on secondary market
  createdAt: any;
  updatedAt: any;
}

export interface GetUserOrdersResponse {
  success: boolean;
  orders: OrderEntity[]; // All orders (for backward compatibility)
  sellOrders: OrderEntity[]; // Orders where user is seller
  buyOrders: OrderEntity[]; // Orders where user is buyer
  count: number; // Total count
  sellOrdersCount: number; // Count of sell orders
  buyOrdersCount: number; // Count of buy orders
  userId: string;
}

export interface CompletePurchaseResponse {
  success: boolean;
  message: string;
  netAmountToSeller: number;
  feeAmount: number;
  order: {
    id: string;
    number: number;
    status: string;
  };
}

export async function getUserOrdersByTgId(
  tgId: string
): Promise<GetUserOrdersResponse> {
  try {
    const response = await axios.post(
      getFunctionUrl("getUserOrdersByBot"),
      {
        data: {
          tgId: tgId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.result;
  } catch (error) {
    console.error("Error getting user orders:", error);
    throw new Error("Failed to get user orders");
  }
}

export async function completePurchaseByBot(
  orderId: string
): Promise<CompletePurchaseResponse> {
  try {
    const response = await axios.post(
      getFunctionUrl("completePurchaseByBot"),
      {
        data: {
          orderId: orderId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.result;
  } catch (error) {
    console.error("Error completing purchase by bot:", error);
    if (axios.isAxiosError(error) && error.response) {
      const errorMessage =
        error.response.data?.error?.message ?? "Failed to complete purchase";
      throw new Error(errorMessage);
    }
    throw new Error("Failed to complete purchase");
  }
}

export function formatOrderForDisplay(order: OrderEntity): string {
  const statusEmoji = {
    active: "🟡",
    paid: "🟠",
    gift_sent_to_relayer: "🎁",
    fulfilled: "✅",
    cancelled: "❌",
  };

  const emoji = statusEmoji[order.status] || "⚪";

  return `${emoji} Order #${order.number ?? 0}\n`;
}

export function getCompletableOrders(orders: OrderEntity[]): OrderEntity[] {
  return orders.filter((order) => order.status === "paid" && order.buyerId);
}

export function getGiftReadyOrders(orders: OrderEntity[]): OrderEntity[] {
  return orders.filter(
    (order) => order.status === "gift_sent_to_relayer" && order.buyerId
  );
}

export async function sendGiftToRelayer(
  orderId: string,
  owned_gift_id: string
): Promise<any> {
  try {
    const response = await axios.post(
      getFunctionUrl("sendGiftToRelayerByBot"),
      {
        data: {
          orderId: orderId,
          botToken: BOT_TOKEN,
          owned_gift_id,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.result;
  } catch (error) {
    console.error("Error sending gift to relayer:", error);
    throw new Error("Failed to send gift to relayer");
  }
}

export async function getOrderByIdByBot(orderId: string): Promise<any> {
  try {
    const response = await axios.post(
      getFunctionUrl("getOrderByIdByBot"),
      {
        data: {
          orderId: orderId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.result;
  } catch (error) {
    console.error("Error getting order by ID:", error);
    throw new Error("Failed to get order by ID");
  }
}
